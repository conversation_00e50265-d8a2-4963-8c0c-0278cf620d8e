[package]
name = "bridge"
version = "0.1.0"
description = "Created with Anchor"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "bridge"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []
solayer = []

[dependencies]
anchor-lang = { version = "0.30.1", features = ["init-if-needed"] }
anchor-spl = { version = "0.30.1", features = ["metadata"] }
bytemuck = { version = "1.20.0", features = ["min_const_generics"] }
solana-program = "1.18.20"
mpl-token-metadata = "4.1.2"
five8_const = "0.1.4"